<template>
  <div class="resource-search-config">
    <div class="search-input-wrapper">
      <!-- 复用现有的搜索输入组件 -->
      <SearchInput
        ref="searchInputRef"
        classType="config"
        :CITypeGroup="CITypeGroup"
        :allAttributesList="allAttributesList"
        :searchValue="config.searchValue"
        :selectCITypeIds="config.selectCITypeIds"
        :expression="config.expression"
        :isColumnSearch="false"
        :isLoading="false"
        @changeFilter="handleChangeFilter"
        @updateAllAttributesList="updateAllAttributesList"
        @saveCondition="handleSaveCondition"
      />
    </div>

    <!-- 搜索预览 -->
    <div class="search-preview-section">
      <a-form-model-item label="搜索预览" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <div class="search-preview">
          <a-tag v-if="searchPreview" color="blue">{{ searchPreview }}</a-tag>
          <span v-else class="preview-placeholder">请配置搜索条件</span>
        </div>
      </a-form-model-item>
    </div>
  </div>
</template>

<script>
import SearchInput from '@/modules/cmdb/views/resource_search_2/resourceSearch/components/searchInput.vue'
import { getCITypeAttributesByTypeIds } from '@/modules/cmdb/api/CITypeAttr'

export default {
  name: 'ResourceSearchConfig',
  components: {
    SearchInput
  },
  props: {
    value: {
      type: Object,
      default: () => ({
        searchValue: '',
        selectCITypeIds: [],
        expression: ''
      })
    },
    CITypeGroup: {
      type: Array,
      default: () => []
    },
    allCITypes: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      config: {
        searchValue: '',
        selectCITypeIds: [],
        expression: ''
      },
      allAttributesList: []
    }
  },
  computed: {
    // 搜索预览 - 复用SearchInput组件的逻辑
    searchPreview() {
      const textArray = []
      if (this.config.selectCITypeIds?.length) {
        textArray.push(`_type:(${this.config.selectCITypeIds.join(';')})`)
      }

      // 解析expression中的筛选条件
      const regQ = /(?<=q=).+(?=&)|(?<=q=).+$/g
      const exp = this.config.expression.match(regQ) ? this.config.expression.match(regQ)[0] : this.config.expression
      if (exp) {
        textArray.push(exp)
      }

      if (this.config.searchValue) {
        textArray.push(`*${this.config.searchValue}*`)
      }

      return textArray.length ? `q=${textArray.join(',')}` : ''
    }
  },
  watch: {
    value: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val) {
          this.config = { ...val }
          this.loadCITypeAttributes()
        }
      }
    },
    'config.selectCITypeIds': {
      handler() {
        this.loadCITypeAttributes()
      }
    }
  },
  methods: {
    // 处理搜索条件变化
    handleChangeFilter(data) {
      this.config[data.name] = data.value
      this.$emit('input', { ...this.config })
    },

    // 更新属性列表
    updateAllAttributesList(value) {
      this.allAttributesList = value
    },

    // 保存搜索条件
    handleSaveCondition() {
      // 在合规检查配置中，不需要实际执行搜索，只需要保存配置
      this.$emit('input', { ...this.config })
    },

    // 加载CI类型属性
    async loadCITypeAttributes() {
      if (this.config.selectCITypeIds && this.config.selectCITypeIds.length > 0) {
        try {
          // 修复API参数格式：将数组转换为逗号分隔的字符串
          const response = await getCITypeAttributesByTypeIds({
            type_ids: this.config.selectCITypeIds.join(',')
          })
          this.allAttributesList = response.attributes || []
        } catch (error) {
          console.error('加载CI类型属性失败:', error)
          this.allAttributesList = []
        }
      } else {
        this.allAttributesList = []
      }
    }
  }
}
</script>

<style lang="less" scoped>
.resource-search-config {
  .search-input-wrapper {
    margin-bottom: 16px;

    // 覆盖SearchInput组件的样式，适配配置模式
    /deep/ .search-input {
      .search-area {
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        padding: 12px;
        background-color: #fafafa;
      }

      .expression-display {
        margin-top: 8px;
        padding: 8px;
        background-color: #f6f8fa;
        border: 1px solid #e1e4e8;
        border-radius: 4px;
        font-size: 12px;

        .expression-display-text {
          color: #24292e;
          font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
        }
      }
    }
  }

  .search-preview-section {
    .search-preview {
      .preview-placeholder {
        color: #999;
        font-style: italic;
      }
    }
  }
}
</style>
