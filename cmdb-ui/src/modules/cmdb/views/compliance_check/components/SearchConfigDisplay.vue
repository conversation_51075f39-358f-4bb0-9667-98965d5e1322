<template>
  <div class="search-config-display">
    <!-- 普通搜索配置展示 -->
    <div v-if="type === 'resource'" class="resource-search-display">
      <a-descriptions :column="1" size="small">
        <a-descriptions-item label="搜索关键词">
          {{ config.searchValue || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="CI类型">
          <div v-if="config.selectCITypeIds && config.selectCITypeIds.length > 0">
            <a-tag v-for="typeId in config.selectCITypeIds" :key="typeId" style="margin-bottom: 4px;">
              {{ getCITypeName(typeId) }}
            </a-tag>
          </div>
          <span v-else>全部类型</span>
        </a-descriptions-item>
        <a-descriptions-item label="筛选条件">
          <code v-if="config.expression" class="expression-code">{{ config.expression }}</code>
          <span v-else>-</span>
        </a-descriptions-item>
        <a-descriptions-item label="搜索表达式">
          <code class="expression-code">{{ getResourceSearchExpression() }}</code>
        </a-descriptions-item>
      </a-descriptions>
    </div>

    <!-- 关系搜索配置展示 -->
    <div v-else class="relation-search-display">
      <a-descriptions :column="1" size="small">
        <a-descriptions-item label="源CI类型">
          {{ config.sourceCIType ? getCITypeName(config.sourceCIType) : '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="源CI搜索条件">
          {{ config.sourceCITypeSearchValue || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="源CI筛选表达式">
          <code v-if="config.sourceExpression" class="expression-code">{{ config.sourceExpression }}</code>
          <span v-else>-</span>
        </a-descriptions-item>
        <a-descriptions-item label="目标CI类型">
          <div v-if="config.targetCITypes && config.targetCITypes.length > 0">
            <a-tag v-for="typeId in config.targetCITypes" :key="typeId" style="margin-bottom: 4px;">
              {{ getCITypeName(typeId) }}
            </a-tag>
          </div>
          <span v-else>-</span>
        </a-descriptions-item>
        <a-descriptions-item label="目标CI筛选表达式">
          <code v-if="config.targetExpression" class="expression-code">{{ config.targetExpression }}</code>
          <span v-else>-</span>
        </a-descriptions-item>
        <a-descriptions-item label="关系路径">
          {{ config.selectedPath ? `路径ID: ${config.selectedPath}` : '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="缺失关系搜索">
          {{ getMissingSearchText(config.missingSearch) }}
        </a-descriptions-item>
      </a-descriptions>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SearchConfigDisplay',
  props: {
    config: {
      type: Object,
      required: true
    },
    type: {
      type: String,
      required: true,
      validator: value => ['resource', 'relation'].includes(value)
    },
    CITypeGroup: {
      type: Array,
      default: () => []
    },
    allCITypes: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    // 获取CI类型名称
    getCITypeName(ciTypeId) {
      // 首先从allCITypes中查找
      if (this.allCITypes && this.allCITypes.length > 0) {
        const ciType = this.allCITypes.find(ct => ct.id === ciTypeId)
        if (ciType) {
          return ciType.alias || ciType.name
        }
      }

      // 如果allCITypes中没有，从CITypeGroup中查找
      for (const group of this.CITypeGroup) {
        if (group.ci_types) {
          const ciType = group.ci_types.find(ct => ct.id === ciTypeId)
          if (ciType) {
            return ciType.alias || ciType.name
          }
        }
      }

      // 如果都没找到，返回默认显示
      return `CI类型${ciTypeId}`
    },

    // 获取普通搜索表达式
    getResourceSearchExpression() {
      const parts = []

      // CI类型
      if (this.config.selectCITypeIds && this.config.selectCITypeIds.length > 0) {
        parts.push(`_type:(${this.config.selectCITypeIds.join(';')})`)
      }

      // 高级筛选
      if (this.config.expression) {
        parts.push(this.config.expression)
      }

      // 搜索关键词
      if (this.config.searchValue) {
        parts.push(`*${this.config.searchValue}*`)
      }

      return parts.length > 0 ? `q=${parts.join(',')}` : 'q='
    },

    // 获取缺失关系搜索文本
    getMissingSearchText(missingSearch) {
      const missingMap = {
        '': '不启用',
        'upstream': '上游缺失',
        'downstream': '下游缺失'
      }
      return missingMap[missingSearch] || missingSearch
    }
  }
}
</script>

<style lang="less" scoped>
.search-config-display {
  .expression-code {
    background-color: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 3px;
    padding: 2px 4px;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 12px;
    color: #24292e;
  }
}
</style>
