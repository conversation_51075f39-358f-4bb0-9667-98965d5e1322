<template>
  <a-drawer
    :title="$t('cmdb.complianceCheck.viewTask')"
    :visible="visible"
    :width="1000"
    @close="handleClose"
  >
    <a-tabs v-if="task">
      <!-- 任务配置标签页 -->
      <a-tab-pane key="config" :tab="$t('cmdb.complianceCheck.taskConfig')">
        <TaskConfigView
          :task="task"
          :CITypeGroup="CITypeGroup"
          :allCITypes="allCITypes"
        />
      </a-tab-pane>

      <!-- 执行历史标签页 -->
      <a-tab-pane key="history" :tab="$t('cmdb.complianceCheck.executionHistory')">
        <div class="execution-history">
          <a-table
            :columns="historyColumns"
            :dataSource="executionHistory"
            :pagination="historyPagination"
            :loading="historyLoading"
            rowKey="id"
            @change="handleHistoryTableChange"
          >
            <template slot="status" slot-scope="status">
              <a-badge :status="getStatusBadge(status)" :text="getStatusText(status)" />
            </template>

            <template slot="duration" slot-scope="duration">
              {{ formatDuration(duration) }}
            </template>

            <template slot="actions" slot-scope="text, record">
              <a-button-group size="small">
                <a-button @click="viewReport(record)" :disabled="record.status !== 'success'">
                  {{ $t('cmdb.complianceCheck.viewReport') }}
                </a-button>
                <a-button @click="downloadReport(record)" :disabled="record.status !== 'success'">
                  {{ $t('cmdb.complianceCheck.downloadReport') }}
                </a-button>
              </a-button-group>
            </template>
          </a-table>
        </div>
      </a-tab-pane>

      <!-- 实时监控标签页 -->
      <a-tab-pane
        key="monitor"
        :tab="$t('cmdb.complianceCheck.realTimeMonitor')"
        v-if="task.status === 'running'"
      >
        <TaskMonitor :taskId="task.id" />
      </a-tab-pane>
    </a-tabs>

    <!-- 报告查看弹窗 -->
    <a-modal
      :title="$t('cmdb.complianceCheck.reportTitle')"
      :visible="reportVisible"
      :width="1200"
      :footer="null"
      @cancel="handleReportClose"
    >
      <ReportView
        v-if="currentReport"
        :report="currentReport"
        :CITypeGroup="CITypeGroup"
        :allCITypes="allCITypes"
      />
    </a-modal>
  </a-drawer>
</template>

<script>
import { getTaskExecutionHistory, getExecutionReport, downloadExecutionReport } from '@/modules/cmdb/api/complianceCheck'
import { getStatusBadge, getStatusText, formatDuration } from '../utils/scheduleUtils'
import { mockExecutionHistory, mockReport } from '../mockData'
import TaskConfigView from './TaskConfigView.vue'
import TaskMonitor from './TaskMonitor.vue'
import ReportView from './ReportView.vue'

export default {
  name: 'TaskDetail',
  components: {
    TaskConfigView,
    TaskMonitor,
    ReportView
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    task: {
      type: Object,
      default: null
    },
    CITypeGroup: {
      type: Array,
      default: () => []
    },
    allCITypes: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      executionHistory: [],
      historyLoading: false,
      historyPagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `共 ${total} 条记录，显示第 ${range[0]}-${range[1]} 条`
      },
      reportVisible: false,
      currentReport: null,
      historyColumns: [
        {
          title: this.$t('cmdb.complianceCheck.executionId'),
          dataIndex: 'id',
          key: 'id',
          width: 120
        },
        {
          title: this.$t('cmdb.complianceCheck.executionStartTime'),
          dataIndex: 'executeTime',
          key: 'executeTime',
          width: 180
        },
        {
          title: this.$t('cmdb.complianceCheck.executionDuration'),
          dataIndex: 'duration',
          key: 'duration',
          width: 120,
          scopedSlots: { customRender: 'duration' }
        },
        {
          title: this.$t('cmdb.complianceCheck.executionStatus'),
          dataIndex: 'status',
          key: 'status',
          width: 100,
          scopedSlots: { customRender: 'status' }
        },
        {
          title: this.$t('cmdb.complianceCheck.violationCount'),
          dataIndex: 'violationCount',
          key: 'violationCount',
          width: 100
        },
        {
          title: this.$t('cmdb.complianceCheck.actions'),
          key: 'actions',
          width: 200,
          scopedSlots: { customRender: 'actions' }
        }
      ]
    }
  },
  watch: {
    visible(val) {
      if (val && this.task) {
        this.loadExecutionHistory()
      }
    }
  },
  methods: {
    getStatusBadge,
    getStatusText,
    formatDuration,

    // 加载执行历史
    async loadExecutionHistory() {
      if (!this.task) return

      this.historyLoading = true
      try {
        const params = {
          page: this.historyPagination.current,
          page_size: this.historyPagination.pageSize
        }

        try {
          const response = await getTaskExecutionHistory(this.task.id, params)
          this.executionHistory = response.executions || []
          this.historyPagination.total = response.total || 0
        } catch (apiError) {
          // 如果API调用失败，使用模拟数据
          console.warn('API调用失败，使用模拟数据:', apiError)
          this.executionHistory = mockExecutionHistory.filter(h => h.taskId === this.task.id)
          this.historyPagination.total = this.executionHistory.length
        }
      } catch (error) {
        console.error('加载执行历史失败:', error)
        this.$message.error('加载执行历史失败')
      } finally {
        this.historyLoading = false
      }
    },

    // 历史表格变化处理
    handleHistoryTableChange(pagination) {
      this.historyPagination.current = pagination.current
      this.historyPagination.pageSize = pagination.pageSize
      this.loadExecutionHistory()
    },

    // 查看报告
    async viewReport(execution) {
      try {
        try {
          const response = await getExecutionReport(execution.id)
          this.currentReport = response.report
        } catch (apiError) {
          // 如果API调用失败，使用模拟数据
          console.warn('API调用失败，使用模拟数据:', apiError)
          this.currentReport = mockReport
        }
        this.reportVisible = true
      } catch (error) {
        console.error('加载报告失败:', error)
        this.$message.error('加载报告失败')
      }
    },

    // 下载报告
    async downloadReport(execution) {
      try {
        const response = await downloadExecutionReport(execution.id, 'excel')
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `合规检查报告_${execution.id}.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        this.$message.success('报告下载成功')
      } catch (error) {
        console.error('下载报告失败:', error)
        this.$message.error('下载报告失败')
      }
    },

    // 关闭抽屉
    handleClose() {
      this.$emit('close')
    },

    // 关闭报告弹窗
    handleReportClose() {
      this.reportVisible = false
      this.currentReport = null
    }
  }
}
</script>

<style lang="less" scoped>
.execution-history {
  padding: 16px 0;
}
</style>
