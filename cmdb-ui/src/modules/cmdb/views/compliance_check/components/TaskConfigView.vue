<template>
  <div class="task-config-view">
    <!-- 基本信息 -->
    <div class="config-section">
      <h3>{{ $t('cmdb.complianceCheck.basicInfo') }}</h3>
      <a-descriptions :column="2" bordered>
        <a-descriptions-item :label="$t('cmdb.complianceCheck.taskName')">
          {{ task.name }}
        </a-descriptions-item>
        <a-descriptions-item :label="$t('cmdb.complianceCheck.taskDescription')">
          {{ task.description || '-' }}
        </a-descriptions-item>
        <a-descriptions-item :label="$t('cmdb.complianceCheck.enabled')">
          <a-badge
            :status="task.enabled ? 'success' : 'default'"
            :text="task.enabled ? $t('cmdb.complianceCheck.enabled') : $t('cmdb.complianceCheck.disabled')"
          />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('cmdb.complianceCheck.taskStatus')">
          <a-badge :status="getStatusBadge(task.status)" :text="getStatusText(task.status)" />
        </a-descriptions-item>
        <a-descriptions-item :label="$t('cmdb.complianceCheck.createTime')">
          {{ task.createTime }}
        </a-descriptions-item>
        <a-descriptions-item :label="$t('cmdb.complianceCheck.updateTime')">
          {{ task.updateTime }}
        </a-descriptions-item>
      </a-descriptions>
    </div>

    <!-- 搜索条件 -->
    <div class="config-section">
      <h3>{{ $t('cmdb.complianceCheck.searchConditions') }}</h3>
      <div
        v-for="(search, index) in task.searches"
        :key="index"
        class="search-condition-card"
      >
        <div class="search-condition-header">
          <h4>{{ $t('cmdb.complianceCheck.searchCondition') }} {{ index + 1 }}</h4>
          <a-tag :color="search.type === 'resource' ? 'blue' : 'green'">
            {{ getSearchTypeText(search.type) }}
          </a-tag>
        </div>

        <SearchConfigDisplay
          :config="search.config"
          :type="search.type"
          :CITypeGroup="CITypeGroup"
          :allCITypes="allCITypes"
        />
      </div>
    </div>

    <!-- 执行计划 -->
    <div class="config-section">
      <h3>{{ $t('cmdb.complianceCheck.executionPlan') }}</h3>
      <a-descriptions :column="2" bordered>
        <a-descriptions-item :label="$t('cmdb.complianceCheck.executionType')">
          {{ getExecutionTypeText(task.schedule.type) }}
        </a-descriptions-item>
        <a-descriptions-item
          v-if="task.schedule.type !== 'once'"
          :label="$t('cmdb.complianceCheck.executionTime')"
        >
          {{ task.schedule.time }}
        </a-descriptions-item>
        <a-descriptions-item
          v-if="task.schedule.type === 'weekly'"
          :label="$t('cmdb.complianceCheck.executionDate')"
        >
          {{ getWeekdaysText(task.schedule.weekdays) }}
        </a-descriptions-item>
        <a-descriptions-item
          v-if="task.schedule.type === 'monthly'"
          :label="$t('cmdb.complianceCheck.executionDate')"
        >
          {{ $t('cmdb.complianceCheck.monthDay', { day: task.schedule.monthday }) }}
        </a-descriptions-item>
        <a-descriptions-item :label="$t('cmdb.complianceCheck.lastExecuteTime')">
          {{ task.lastExecuteTime || '-' }}
        </a-descriptions-item>
        <a-descriptions-item :label="$t('cmdb.complianceCheck.nextExecuteTime')">
          {{ task.nextExecuteTime || '-' }}
        </a-descriptions-item>
      </a-descriptions>
    </div>

    <!-- 通知设置 -->
    <div class="config-section">
      <h3>{{ $t('cmdb.complianceCheck.notificationSettings') }}</h3>
      <a-descriptions :column="2" bordered>
        <a-descriptions-item :label="$t('cmdb.complianceCheck.enableNotification')">
          <a-badge
            :status="task.notification.enabled ? 'success' : 'default'"
            :text="task.notification.enabled ? '已启用' : '未启用'"
          />
        </a-descriptions-item>
        <a-descriptions-item
          v-if="task.notification.enabled"
          :label="$t('cmdb.complianceCheck.notificationEmails')"
        >
          <div v-if="task.notification.emails && task.notification.emails.length > 0">
            <a-tag v-for="email in task.notification.emails" :key="email" style="margin-bottom: 4px;">
              {{ email }}
            </a-tag>
          </div>
          <span v-else>-</span>
        </a-descriptions-item>
        <a-descriptions-item
          v-if="task.notification.enabled"
          :label="$t('cmdb.complianceCheck.notificationCondition')"
        >
          {{ getNotificationConditionText(task.notification.condition) }}
        </a-descriptions-item>
      </a-descriptions>
    </div>
  </div>
</template>

<script>
import { getStatusBadge, getStatusText, getSearchTypeText } from '../utils/scheduleUtils'
import SearchConfigDisplay from './SearchConfigDisplay.vue'

export default {
  name: 'TaskConfigView',
  components: {
    SearchConfigDisplay
  },
  props: {
    task: {
      type: Object,
      required: true
    },
    CITypeGroup: {
      type: Array,
      default: () => []
    },
    allCITypes: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    getStatusBadge,
    getStatusText,
    getSearchTypeText,

    // 获取执行类型文本
    getExecutionTypeText(type) {
      const typeMap = {
        'once': this.$t('cmdb.complianceCheck.executeOnce'),
        'daily': this.$t('cmdb.complianceCheck.executeDaily'),
        'weekly': this.$t('cmdb.complianceCheck.executeWeekly'),
        'monthly': this.$t('cmdb.complianceCheck.executeMonthly')
      }
      return typeMap[type] || type
    },

    // 获取星期文本
    getWeekdaysText(weekdays) {
      if (!weekdays || weekdays.length === 0) return '-'

      const weekdayMap = {
        '0': this.$t('cmdb.complianceCheck.sunday'),
        '1': this.$t('cmdb.complianceCheck.monday'),
        '2': this.$t('cmdb.complianceCheck.tuesday'),
        '3': this.$t('cmdb.complianceCheck.wednesday'),
        '4': this.$t('cmdb.complianceCheck.thursday'),
        '5': this.$t('cmdb.complianceCheck.friday'),
        '6': this.$t('cmdb.complianceCheck.saturday')
      }

      return weekdays.map(day => weekdayMap[day] || day).join(', ')
    },

    // 获取通知条件文本
    getNotificationConditionText(condition) {
      const conditionMap = {
        'always': this.$t('cmdb.complianceCheck.alwaysNotify'),
        'violation': this.$t('cmdb.complianceCheck.violationOnlyNotify')
      }
      return conditionMap[condition] || condition
    }
  }
}
</script>

<style lang="less" scoped>
.task-config-view {
  padding: 16px 0;

  .config-section {
    margin-bottom: 32px;

    h3 {
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 500;
      color: #262626;
    }
  }

  .search-condition-card {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 16px;

    .search-condition-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h4 {
        margin: 0;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}
</style>
