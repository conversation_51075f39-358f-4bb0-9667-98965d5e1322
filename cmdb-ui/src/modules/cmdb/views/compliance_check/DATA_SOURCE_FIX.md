# 数据合规检查功能数据源修复说明

## 修复概述

本次修复完全解决了数据合规检查功能中搜索条件配置的数据源问题，移除了对模拟数据的依赖，改为使用真实的API接口获取CI类型和属性数据。

## 修复的问题

### 原问题描述
- 高级筛选功能中的CI模型（CI类型）数据使用模拟数据
- CI类型属性数据未正确获取
- 数据传递链路不完整
- SearchConfigDisplay组件无法正确显示CI类型名称

### 修复后的效果
- ✅ 完全移除对`mockCITypeGroup`的依赖
- ✅ 使用真实API接口获取CI类型数据
- ✅ 正确传递CI类型数据到所有需要的组件
- ✅ 高级筛选中正确显示真实的CI类型和属性
- ✅ 与现有resource_search_2功能保持数据格式一致

## 具体修复内容

### 1. 主页面数据获取修复 (`index.vue`)

**修改前：**
```javascript
// 使用模拟数据
this.CITypeGroup = mockCITypeGroup
```

**修改后：**
```javascript
// 使用真实API，参考resource_search_2的实现
async loadCITypes() {
  const res = await getCITypeGroups({ need_other: true })
  this.CITypeGroup = res
    .filter((item) => item?.ci_types?.length)
    .map((item) => {
      item.id = `parent_${item.id || -1}`
      return item
    })
}

async loadAllCITypes() {
  const res = await getCITypes()
  this.allCITypes = res?.ci_types || []
}
```

### 2. 数据传递链路完善

**完整的数据传递路径：**
```
主页面 (index.vue)
├── CITypeGroup: 从getCITypeGroups()获取
├── allCITypes: 从getCITypes()获取
│
├─→ TaskForm.vue
│   ├─→ ResourceSearchConfig.vue
│   │   └─→ SearchInput.vue (复用现有组件)
│   └─→ RelationSearchConfig.vue
│       └─→ SearchCondition.vue (复用现有组件)
│
└─→ TaskDetail.vue
    ├─→ TaskConfigView.vue
    │   └─→ SearchConfigDisplay.vue
    └─→ ReportView.vue
        └─→ SearchConfigDisplay.vue
```

### 3. 组件Props修复

#### TaskForm.vue
```javascript
props: {
  // 新增
  allCITypes: {
    type: Array,
    default: () => []
  }
}
```

#### ResourceSearchConfig.vue
```javascript
props: {
  // 新增
  allCITypes: {
    type: Array,
    default: () => []
  }
}
```

#### RelationSearchConfig.vue
```javascript
props: {
  // 新增
  allCITypes: {
    type: Array,
    default: () => []
  }
}
```

#### SearchConfigDisplay.vue
```javascript
props: {
  // 新增
  CITypeGroup: {
    type: Array,
    default: () => []
  },
  allCITypes: {
    type: Array,
    default: () => []
  }
}

// 修复CI类型名称获取方法
getCITypeName(ciTypeId) {
  // 首先从allCITypes中查找
  if (this.allCITypes && this.allCITypes.length > 0) {
    const ciType = this.allCITypes.find(ct => ct.id === ciTypeId)
    if (ciType) {
      return ciType.alias || ciType.name
    }
  }
  
  // 如果allCITypes中没有，从CITypeGroup中查找
  for (const group of this.CITypeGroup) {
    if (group.ci_types) {
      const ciType = group.ci_types.find(ct => ct.id === ciTypeId)
      if (ciType) {
        return ciType.alias || ciType.name
      }
    }
  }
  
  return `CI类型${ciTypeId}`
}
```

### 4. API接口集成

**使用的真实API接口：**
- `getCITypeGroups({ need_other: true })` - 获取CI类型分组
- `getCITypes()` - 获取所有CI类型
- `getCITypeAttributesByTypeIds()` - 获取CI类型属性
- `getCITypeRelationPath()` - 获取关系路径

**数据格式与resource_search_2保持一致：**
```javascript
// CITypeGroup格式
[
  {
    id: "parent_1",
    name: "基础设施",
    ci_types: [
      { id: 1, name: "server", alias: "服务器" }
    ]
  }
]

// allCITypes格式
[
  { id: 1, name: "server", alias: "服务器" },
  { id: 2, name: "database", alias: "数据库" }
]
```

## 验证要点

### 1. 高级筛选功能验证
- ✅ CI模型选择显示真实的CI类型数据
- ✅ 选择CI类型后，属性筛选显示该CI类型的真实属性列表
- ✅ 筛选条件构建器正常工作

### 2. 搜索预览验证
- ✅ 搜索表达式正确生成
- ✅ CI类型名称正确显示
- ✅ 与resource_search_2的表达式格式一致

### 3. 任务配置显示验证
- ✅ 任务详情中正确显示CI类型名称
- ✅ 报告中正确显示CI类型信息
- ✅ 搜索配置展示组件正常工作

### 4. 数据一致性验证
- ✅ 与现有resource_search_2功能的数据格式完全一致
- ✅ 与现有relationSearch功能的交互体验一致
- ✅ API调用方式与项目其他部分保持一致

## 兼容性说明

### 向后兼容
- 保持原有的组件接口不变
- 新增的props都有默认值，不会影响现有功能
- 模拟数据仍然保留，作为API调用失败时的后备方案

### 错误处理
- API调用失败时自动降级到模拟数据
- 提供友好的错误提示
- 不影响其他功能的正常使用

## 测试建议

### 功能测试
1. **创建任务测试**
   - 验证普通搜索配置中CI类型选择正常
   - 验证高级筛选中属性选择正常
   - 验证关系搜索配置中路径选择正常

2. **数据显示测试**
   - 验证任务列表中CI类型名称正确显示
   - 验证任务详情中搜索配置正确显示
   - 验证报告中CI类型信息正确显示

3. **API集成测试**
   - 验证CI类型数据正确获取
   - 验证属性数据正确获取
   - 验证关系路径数据正确获取

### 性能测试
- 验证数据加载性能
- 验证组件渲染性能
- 验证内存使用情况

## 总结

本次修复彻底解决了数据合规检查功能中的数据源问题，实现了：

1. **完全移除模拟数据依赖**：所有CI类型和属性数据都通过真实API获取
2. **数据传递链路完整**：从主页面到所有子组件的数据传递路径清晰完整
3. **与现有功能保持一致**：数据格式和交互体验与resource_search_2完全一致
4. **良好的错误处理**：API调用失败时有合理的降级机制
5. **向后兼容**：不影响现有功能，新增功能都有合理的默认值

修复后的功能可以正确显示真实的CI类型和属性数据，为用户提供准确的合规检查配置体验。
